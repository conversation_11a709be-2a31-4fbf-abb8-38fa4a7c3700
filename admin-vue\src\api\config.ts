// API配置
export const API_CONFIG = {
  // 新版API基础地址
  NEW_API_BASE_URL: import.meta.env.VITE_NEW_API_BASE_URL || 'https://api.myaitts.com',
  // 旧版API基础地址（兼容模式）
  LEGACY_API_BASE_URL: import.meta.env.VITE_LEGACY_API_BASE_URL || 'https://cardapi.aispeak.top',
  // 保留旧配置用于向后兼容
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'https://cardapi.aispeak.top',
  TIMEOUT: parseInt(import.meta.env.VITE_API_TIMEOUT || '10000'),
  RETRY_COUNT: parseInt(import.meta.env.VITE_API_RETRY_COUNT || '3')
}

// API基础URL映射
export const API_BASE_URL_MAP = {
  new: API_CONFIG.NEW_API_BASE_URL,
  legacy: API_CONFIG.LEGACY_API_BASE_URL
} as const

// API模式端点映射
export const API_ENDPOINT_MAP = {
  new: {
    // 认证接口
    AUTH_LOGIN: '/api/auth/login',

    // 用户管理接口
    ADMIN_USERS: '/api/admin/users',
    ADMIN_USER_DETAIL: '/api/admin/users',
    ADMIN_USER_VIP: '/api/admin/users',

    // 卡密管理接口
    ADMIN_CARDS: '/api/admin/cards',
    ADMIN_CARDS_GENERATE: '/api/admin/cards/generate',
    ADMIN_CARDS_PACKAGES: '/api/admin/cards/packages',

    // 系统统计接口 - 使用标准的系统统计接口
    ADMIN_STATS: '/api/admin/stats',

    // 用量统计接口 - 分离全局统计和用户列表
    USER_QUOTA: '/api/admin/users',              // 用户列表数据（用于表格展示）
    USER_STATS: '/api/admin/usage/summary',      // 全局用量统计数据（用于统计卡片）
    USER_PROFILE: '/api/user/profile',

    // 操作接口
    CARD_EDIT: '/api/admin/cards/edit',
    CARD_DELETE: '/api/admin/cards/delete'
  },
  legacy: {
    // 认证接口
    AUTH_LOGIN: '/auth',

    // 用户管理接口
    ADMIN_USERS: '/users',
    ADMIN_USER_DETAIL: '/users',
    ADMIN_USER_VIP: '/api/admin/users',

    // 卡密管理接口
    ADMIN_CARDS: '/cards',
    ADMIN_CARDS_GENERATE: '/card-generate',
    ADMIN_CARDS_PACKAGES: '',  // 兼容模式无套餐接口，使用静态配置

    // 系统统计接口
    ADMIN_STATS: '/api/admin/users/stats',

    // 用量统计接口 (兼容模式使用新版API端点)
    USER_QUOTA: '/api/admin/users/usage',
    USER_STATS: '/api/admin/users/stats',
    USER_PROFILE: '/api/admin/users/usage',

    // 操作接口
    CARD_EDIT: '/edit-card',
    CARD_DELETE: '/delete-card'
  }
} as const

// 保留旧的API_ENDPOINTS用于向后兼容 (已废弃，请使用APIRouter)
export const API_ENDPOINTS = {
  // 认证接口
  AUTH_LOGIN: '/api/auth/login',
  AUTH: '/auth',

  // 其他接口已迁移到APIRouter，请使用APIRouter.getEndpoint()
}

// 请求头配置
export const getAuthHeaders = (token?: string) => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json'
  }
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`
  }
  
  return headers
}
